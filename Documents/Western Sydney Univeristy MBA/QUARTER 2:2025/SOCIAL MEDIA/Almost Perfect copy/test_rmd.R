# Test script to verify R Markdown functionality
# This script tests the key components without full rendering

# Working directory should already be correct
cat("Current working directory:", getwd(), "\n")

# Load required packages
packages <- c("igraph", "ggplot2", "dplyr", "knitr")
for (pkg in packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# Test 1: Load lunch data
cat("=== Testing lunch data loading ===\n")
lunch_data <- read.csv("lunch_location2025.csv", stringsAsFactors = FALSE)
lunch_data <- lunch_data[lunch_data$Username != "" & !is.na(lunch_data$Username), ]
cat("Lunch data loaded successfully!\n")
cat("Rows:", nrow(lunch_data), "\n")
print(table(lunch_data$Location))

# Test 2: Create network with real usernames
cat("\n=== Testing network creation ===\n")
set.seed(123)
n_nodes <- 65
n_edges <- 127

# Include real usernames from lunch data
real_usernames <- c("kiiitkaaat", "dofori", "jenny<PERSON>on", "crayeula", "sauagat77",
                   "ankitdha<PERSON>1", "esha_papat", "SharadaKandel", "nimabilbil",
                   "datinguyen", "cringecompiler", "Islam_Merina", "Metakilt",
                   "lapark", "22098152", "snisar", "emdomingo", "austin_darian",
                   "xerzat", "danielsukaria")

additional_usernames <- c("warren", "seaun77", "Natasha", "rogerharrison", "jennyjean",
                         "chriscourtney", "kimbeast", "chines", "criscourtney",
                         paste0("user", 1:(n_nodes - length(real_usernames) - 9)))

node_names <- c(real_usernames, additional_usernames)

# Create edges
edges <- data.frame(
  from = sample(node_names, n_edges, replace = TRUE),
  to = sample(node_names, n_edges, replace = TRUE)
)
edges <- edges[edges$from != edges$to, ]
edges <- unique(edges)

# Create graph
following_graph <- graph_from_data_frame(edges, vertices = node_names, directed = TRUE)
cat("Network created successfully!\n")
cat("Nodes:", vcount(following_graph), "\n")
cat("Edges:", ecount(following_graph), "\n")

# Test 3: Check overlap between network and lunch data
cat("\n=== Testing data overlap ===\n")
network_usernames <- V(following_graph)$name
lunch_usernames <- lunch_data$Username
common_users <- intersect(network_usernames, lunch_usernames)
cat("Common users:", length(common_users), "\n")
cat("Common usernames:", paste(head(common_users, 10), collapse = ", "), "\n")

# Test 4: Test homophily calculation
cat("\n=== Testing homophily analysis ===\n")
calculate_homophily <- function(graph, attribute_data) {
  username_to_location <- setNames(
    as.character(attribute_data$Location),
    attribute_data$Username
  )

  same_location_edges <- 0
  diff_location_edges <- 0
  edge_list <- as_edgelist(graph)

  for (i in 1:nrow(edge_list)) {
    from_username <- edge_list[i, 1]
    to_username <- edge_list[i, 2]

    from_location <- username_to_location[from_username]
    to_location <- username_to_location[to_username]

    if (!is.na(from_location) && !is.na(to_location)) {
      if (from_location == to_location) {
        same_location_edges <- same_location_edges + 1
      } else {
        diff_location_edges <- diff_location_edges + 1
      }
    }
  }

  total_counted_edges <- same_location_edges + diff_location_edges

  if (total_counted_edges > 0) {
    homophily_index <- same_location_edges / total_counted_edges
    location_counts <- table(attribute_data$Location)
    location_props <- location_counts / sum(location_counts)
    expected_homophily <- sum(location_props^2)
    homophily_ratio <- homophily_index / expected_homophily

    return(list(
      same_location_edges = same_location_edges,
      diff_location_edges = diff_location_edges,
      total_counted_edges = total_counted_edges,
      homophily_index = homophily_index,
      expected_homophily = expected_homophily,
      homophily_ratio = homophily_ratio
    ))
  }
  return(NULL)
}

homophily_results <- calculate_homophily(following_graph, lunch_data)

if (!is.null(homophily_results)) {
  cat("Homophily analysis successful!\n")
  cat("Same location edges:", homophily_results$same_location_edges, "\n")
  cat("Different location edges:", homophily_results$diff_location_edges, "\n")
  cat("Homophily ratio:", round(homophily_results$homophily_ratio, 3), "\n")

  if (homophily_results$homophily_ratio > 1.1) {
    interpretation <- "Strong evidence of homophily"
  } else if (homophily_results$homophily_ratio > 0.9) {
    interpretation <- "No evidence of homophily"
  } else {
    interpretation <- "Evidence of heterophily"
  }
  cat("Interpretation:", interpretation, "\n")
} else {
  cat("No homophily analysis possible (no matching edges)\n")
}

cat("\n=== All tests completed successfully! ===\n")
cat("The R Markdown file should work properly.\n")
