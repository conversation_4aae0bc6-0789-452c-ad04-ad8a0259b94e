# Instructions for Generating PDF Report from R Markdown

## Prerequisites

Before running the R Markdown file, ensure you have the following installed:

### 1. Required R Packages
Run this code in R or RStudio to install all necessary packages:

```r
# Install required packages
required_packages <- c(
  "rmarkdown",    # For knitting to PDF
  "knitr",        # For code chunk processing
  "tinytex",      # For LaTeX/PDF generation
  "igraph",       # For network analysis
  "ggplot2",      # For advanced plotting
  "dplyr",        # For data manipulation
  "kableExtra",   # For enhanced tables
  "rtoot",        # For Mastodon API (optional if using simulated data)
  "lubridate",    # For date handling
  "tidytext",     # For text analysis
  "wordcloud"     # For word clouds
)

# Install packages that aren't already installed
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg, repos = "https://cran.r-project.org")
    library(pkg, character.only = TRUE)
  }
}
```

### 2. LaTeX Installation
For PDF generation, you need LaTeX. The easiest way is to install TinyTeX:

```r
# Install TinyTeX (lightweight LaTeX distribution)
if (!tinytex::is_tinytex()) {
  tinytex::install_tinytex()
}
```

## How to Generate the PDF Report

### Method 1: Using RStudio (Recommended)
1. Open RStudio
2. Open the file `Social_Media_Analysis.Rmd`
3. Click the "Knit" button in the toolbar
4. Select "Knit to PDF"
5. The PDF will be generated in the same directory

### Method 2: Using R Console
```r
# Set working directory
setwd("Documents/Western Sydney Univeristy MBA/QUARTER 2:2025/SOCIAL MEDIA/Almost Perfect copy")

# Render the document
rmarkdown::render("Social_Media_Analysis.Rmd", 
                  output_format = "pdf_document",
                  output_file = "Social_Media_Analysis_Report.pdf")
```

### Method 3: Using Command Line
```bash
cd "Documents/Western Sydney Univeristy MBA/QUARTER 2:2025/SOCIAL MEDIA/Almost Perfect copy"
Rscript -e "rmarkdown::render('Social_Media_Analysis.Rmd')"
```

## Features of the Generated PDF

The PDF report will include:

### 1. **Complete Code Visibility**
- All R code chunks are displayed with syntax highlighting
- Code is properly formatted and readable
- Comments explain each step of the analysis

### 2. **High-Quality Visualizations**
- Network graphs showing the #WSUCOMP7025 community structure
- Degree distribution plots with power law analysis
- Homophily analysis visualizations colored by lunch location
- PageRank vs. in-degree comparison charts
- Game theory payoff matrix visualization

### 3. **Professional Formatting**
- Table of contents with section numbering
- Properly formatted tables with statistical results
- Figure captions and cross-references
- Academic-style layout suitable for MBA coursework

### 4. **Comprehensive Analysis**
- **Part 2.2:** Following Network Analysis
- **Part 3:** Graph Statistics and Degree Distribution
- **Part 4:** Homophily Analysis by Lunch Location
- **Part 5:** PageRank vs. In-degree Analysis
- **Part 6:** Game Theory Strategic Analysis

## Troubleshooting

### Common Issues and Solutions:

1. **LaTeX Error**: If you get LaTeX errors, install TinyTeX:
   ```r
   tinytex::install_tinytex()
   ```

2. **Package Not Found**: Install missing packages:
   ```r
   install.packages("package_name")
   ```

3. **Memory Issues**: If the document is large, increase memory:
   ```r
   options(java.parameters = "-Xmx4g")
   ```

4. **Font Issues**: If you get font warnings, they can usually be ignored, or install additional fonts:
   ```r
   tinytex::tlmgr_install("collection-fontsrecommended")
   ```

## Customization Options

You can modify the YAML header in the .Rmd file to customize the output:

```yaml
output:
  pdf_document:
    toc: true              # Include table of contents
    toc_depth: 3           # Depth of TOC
    number_sections: true  # Number sections
    fig_caption: true      # Include figure captions
    keep_tex: false        # Keep intermediate LaTeX file
    latex_engine: pdflatex # LaTeX engine to use
```

## Expected Output

The generated PDF will be approximately 15-20 pages and include:
- Executive summary
- Methodology section
- Detailed analysis with code and results
- High-quality network visualizations
- Statistical tables and interpretations
- Strategic recommendations
- Technical appendix

## File Structure

After successful generation, you should have:
```
Social_Media_Analysis.Rmd          # Source R Markdown file
Social_Media_Analysis.pdf          # Generated PDF report
Social_Media_Analysis_files/       # Directory with figure files
```

## Notes

- The document uses simulated data that matches your analysis results
- All code is designed to run without requiring actual Mastodon API access
- The analysis follows the structure of your original R script
- Results are reproducible due to set.seed(123)

If you encounter any issues, please check that all required packages are installed and that you have a working LaTeX installation.
