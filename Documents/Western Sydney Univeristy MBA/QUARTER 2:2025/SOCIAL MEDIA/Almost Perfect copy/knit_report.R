# Script to knit the R Markdown report to PDF
# Run this script to generate the PDF report

# Install required packages if not already installed
required_packages <- c("rmarkdown", "knitr", "tinytex", "igraph", "ggplot2", 
                      "dplyr", "kable<PERSON>xtra", "rtoot", "lubridate", "tidytext", "wordcloud")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
  }
}

# Install TinyTeX if not already installed (for PDF generation)
if (!tinytex::is_tinytex()) {
  tinytex::install_tinytex()
}

# Set working directory to the location of the Rmd file
setwd("Documents/Western Sydney Univeristy MBA/QUARTER 2:2025/SOCIAL MEDIA/Almost Perfect copy")

# Knit the R Markdown file to PDF
rmarkdown::render("Social_Media_Analysis.Rmd", 
                  output_format = "pdf_document",
                  output_file = "Social_Media_Analysis_Report.pdf")

cat("PDF report generated successfully!\n")
cat("Output file: Social_Media_Analysis_Report.pdf\n")
