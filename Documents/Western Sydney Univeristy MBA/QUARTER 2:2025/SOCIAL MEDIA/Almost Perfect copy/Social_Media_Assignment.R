# Social Media Analytics Assignment - Mastodon Data Analysis using rtoot
# Western Sydney University MBA - Quarter 2:2025
# Course: Social Media Analytics

# Install and load required packages
if (!require(rtoot)) {
  install.packages("rtoot")
}
if (!require(dplyr)) {
  install.packages("dplyr")
}
if (!require(ggplot2)) {
  install.packages("ggplot2")
}
if (!require(lubridate)) {
  install.packages("lubridate")
}
if (!require(tidytext)) {
  install.packages("tidytext")
}
if (!require(wordcloud)) {
  install.packages("wordcloud")
}

library(rtoot)
library(dplyr)
library(ggplot2)
library(lubridate)
library(tidytext)
library(wordcloud)

# ============================================================================
# PART 1: AUTHENTICATION AND SETUP
# ============================================================================

# Check if authentication already exists, if not, set up new authentication
auth_setup()
token_path <- file.path(tools::R_user_dir("rtoot", "config"), "account1.rds")

if (!file.exists(token_path)) {
  cat("Setting up new authentication...\n")
  # Set up authentication for Mastodon
  # Note: This creates a token for accessing Mastodon's API
  auth_setup(name = "account1")
} else {
  cat("Using existing authentication...\n")
}

# Load the authentication token
token <- readRDS(token_path)

# Set default token for the session
options("rtoot_token" = token_path)

cat("Authentication successful! Token loaded from:", token_path, "\n")

# ============================================================================
# PART 1: DATA COLLECTION FROM MASTODON API
# ============================================================================

cat("Collecting real data from Mastodon API...\n")

# Get general information about mastodon.social instance
cat("Getting instance information...\n")
instance_info <- get_instance_general(instance = "mastodon.social")
print("=== MASTODON.SOCIAL INSTANCE INFORMATION ===")
print(instance_info)

# Get instance activity data (last 3 months)
cat("Getting instance activity (last 3 months)...\n")
instance_activity <- get_instance_activity(instance = "mastodon.social")
print("=== INSTANCE ACTIVITY (Last 3 Months) ===")
print(instance_activity)

# Get trending hashtags
cat("Getting trending hashtags...\n")
instance_trends <- get_instance_trends(instance = "mastodon.social")
print("=== TRENDING HASHTAGS ===")
print(instance_trends)

# ============================================================================
# PART 2: CONTENT ANALYSIS
# ============================================================================

# Get public timeline data
cat("Getting public timeline (100 posts)...\n")
public_timeline <- get_timeline_public(instance = "mastodon.social")
print("=== PUBLIC TIMELINE SAMPLE ===")
print(paste("Retrieved", nrow(public_timeline), "posts from public timeline"))

# Get posts with specific hashtag (#rstats)
cat("Getting #rstats posts (100 posts)...\n")
rstats_posts <- get_timeline_hashtag(hashtag = "rstats",
                                    instance = "mastodon.social")

print("=== #RSTATS HASHTAG ANALYSIS ===")
print(paste("Retrieved", nrow(rstats_posts), "posts with #rstats hashtag"))

# Get a specific status for detailed analysis
cat("Getting sample status for detailed analysis...\n")
cat("Getting sample status for detailed analysis...\n")
sample_status <- get_status(id = "114566960013536803",
                           instance = "mastodon.social",
                           token = token)


print("=== SAMPLE STATUS ANALYSIS ===")
print(sample_status)

# Display data collection summary
cat("\n=== DATA COLLECTION SUMMARY ===\n")
cat("Instance activity records:", nrow(instance_activity), "\n")
cat("Trending hashtags:", nrow(instance_trends), "\n")
cat("Public timeline posts:", nrow(public_timeline), "\n")
cat("#rstats posts:", nrow(rstats_posts), "\n")

# ============================================================================
# PART 2.1: WSUCOMP7025 HASHTAG ANALYSIS
# ============================================================================

# Install and load igraph if not already installed
if (!require(igraph)) {
  install.packages("igraph")
}
library(igraph)

# Get posts with #WSUCOMP7025 hashtag
cat("Getting #WSUCOMP7025 posts...\n")
wsu_posts <- get_timeline_hashtag(hashtag = "WSUCOMP7025", 
                                 instance = "mastodon.social")

print("=== #WSUCOMP7025 HASHTAG ANALYSIS ===")
print(paste("Retrieved", nrow(wsu_posts), "posts with #WSUCOMP7025 hashtag"))

# Create a table showing each server and count of #WSUCOMP7025 toots
if (nrow(wsu_posts) > 0) {
  # Extract server from URI
  wsu_posts$server <- gsub("^https?://([^/]+)/.*$", "\\1", wsu_posts$uri)
  
  # Count toots by server
  server_counts <- wsu_posts %>%
    count(server, sort = TRUE) %>%
    rename(toot_count = n)
  
  print("=== SERVER DISTRIBUTION FOR #WSUCOMP7025 TOOTS ===")
  print(server_counts)
} else {
  cat("No #WSUCOMP7025 posts found. This may be a new or unused hashtag.\n")
}

# ============================================================================
# PART 2.2: FOLLOWING GRAPH ANALYSIS
# ============================================================================

# Create following graph if we have posts
if (nrow(wsu_posts) > 0) {
  cat("Creating following graph for #WSUCOMP7025 authors...\n")
  
  # Get unique authors
  authors <- unique(sapply(wsu_posts$account, function(x) x$id))
  cat("Found", length(authors), "unique authors\n")
  
  # Initialize empty graph
  following_graph <- make_empty_graph(directed = TRUE)
  
  # Initialize data structures for graph building
  all_nodes <- c()
  all_edges <- data.frame(from = character(), to = character(), stringsAsFactors = FALSE)
  
  # For each author, get their following list
  for (author_id in authors) {
    cat("Processing author:", author_id, "\n")
    
    # Get author details
    author_account <- get_account(id = author_id, instance = "mastodon.social")
    author_username <- paste0(author_account$username, "@", 
                             gsub("^https?://([^/]+)/.*$", "\\1", author_account$url))
    all_nodes <- c(all_nodes, author_username)
    
    # Get accounts they follow (limited to 20 for performance)
    # Using the correct function: get_account_following instead of get_following
    following <- get_account_following(id = author_id)
    
    if (nrow(following) > 0) {
      for (i in 1:nrow(following)) {
        followed_username <- paste0(following$username[i], "@", 
                                   gsub("^https?://([^/]+)/.*$", "\\1", following$url[i]))
        all_nodes <- c(all_nodes, followed_username)
        
        # Add edge: author follows this account
        all_edges <- rbind(all_edges, 
                          data.frame(from = author_username, 
                                    to = followed_username, 
                                    stringsAsFactors = FALSE))
      }
    }
    
    # Pause to avoid rate limiting
    Sys.sleep(0.2)
  }
  
  # Create graph from edges
  all_nodes <- unique(all_nodes)
  following_graph <- graph_from_data_frame(all_edges, vertices = all_nodes, directed = TRUE)
  
  # Analyze graph components
  components <- components(following_graph)
  component_sizes <- table(components$membership)
  
  cat("\n=== FOLLOWING GRAPH ANALYSIS ===\n")
  cat("Total nodes:", vcount(following_graph), "\n")
  cat("Total edges:", ecount(following_graph), "\n")
  cat("Number of components:", components$no, "\n")
  cat("Component sizes:", paste(component_sizes, collapse = ", "), "\n")
  
  # Find largest component
  largest_comp_id <- which.max(component_sizes)
  largest_comp_nodes <- which(components$membership == largest_comp_id)
  largest_comp <- induced_subgraph(following_graph, largest_comp_nodes)
  
  cat("Largest component size:", vcount(largest_comp), "nodes\n")
  
  # Plot the largest component with improved visualization
  plot_title <- paste("Following Network for #WSUCOMP7025 Authors -",
                     "Largest Component (", vcount(largest_comp), "nodes )")
  
  # Set plot parameters for better visualization
  V(largest_comp)$size <- 5 + 3 * sqrt(degree(largest_comp, mode = "in"))
  V(largest_comp)$color <- rainbow(vcount(largest_comp), alpha = 0.8)
  V(largest_comp)$label.cex <- 0.7
  E(largest_comp)$arrow.size <- 0.3
  
  # Create the plot
  plot(largest_comp, 
       layout = layout_with_fr(largest_comp),
       vertex.label.dist = 0.5,
       vertex.label.color = "black",
       edge.curved = 0.2,
       main = plot_title)
  
  # Add legend
  legend("bottomright", 
         legend = c("Node size = follower count", 
                   paste("Components:", components$no),
                   paste("Largest component:", vcount(largest_comp), "nodes")),
         bty = "n", cex = 0.8)
  
  cat("\nFollowing graph analysis complete!\n")
} else {
  cat("Cannot create following graph: No #WSUCOMP7025 posts found\n")
}



# ============================================================================
# PART 3: GRAPH STATISTICS
# ============================================================================

# Analyze graph components and degree distribution
analyze_graph_statistics <- function(graph) {
  # Basic graph metrics
  components <- components(graph)
  cat("\n=== GRAPH STATISTICS ===\n")
  cat("Nodes:", vcount(graph), "| Edges:", ecount(graph), "\n")
  cat("Components:", components$no, "| Density:", round(edge_density(graph), 4), "\n")
  
  # Calculate in-degree distribution
  in_degrees <- degree(graph, mode = "in")
  degree_dist <- table(in_degrees)
  
  # Plot in-degree distribution on log-log scale with improved clarity
  par(mar = c(5, 5, 4, 2) + 0.1)  # Increase margins for better labels
  
  plot(as.numeric(names(degree_dist)), degree_dist, 
       log = "xy", 
       xlab = "In-degree (log scale)", 
       ylab = "Frequency (log scale)",
       main = "In-degree Distribution (log-log scale)",
       pch = 19,
       cex = 1.2,
       cex.lab = 1.2,
       cex.axis = 1.1,
       cex.main = 1.3)
  
  # Add grid for better readability
  grid(lty = "dotted", col = "lightgray")
  
  # Fit power law if enough data points
  positive_degrees <- in_degrees[in_degrees > 0]
  if(length(positive_degrees) > 5) {
    degree_values <- as.numeric(names(table(positive_degrees)))
    degree_counts <- as.vector(table(positive_degrees))
    
    valid_points <- degree_counts > 0
    if(sum(valid_points) > 2) {
      log_degrees <- log(degree_values[valid_points])
      log_counts <- log(degree_counts[valid_points])
      
      power_law_fit <- lm(log_counts ~ log_degrees)
      power_law_coef <- -coef(power_law_fit)[2]
      
      # Add regression line
      abline(power_law_fit, col = "red", lwd = 2)
      
      # Add coefficient text to plot
      text_x <- max(as.numeric(names(degree_dist))) * 0.5
      text_y <- max(degree_dist) * 0.5
      text_label <- paste("Power Law α =", round(power_law_coef, 2))
      text(text_x, text_y, text_label, cex = 1.2, col = "red")
      
      cat("Power Law coefficient:", round(power_law_coef, 2), "\n")
      cat("Interpretation: Value between 2-3 indicates scale-free network\n")
    }
  }
  
  # Reset plot parameters
  par(mar = c(5, 4, 4, 2) + 0.1)
}

# Run analysis if graph exists
if (exists("following_graph") && vcount(following_graph) > 0) {
  analyze_graph_statistics(following_graph)
} else {
  cat("Network graph not available for statistical analysis\n")
}

# ============================================================================
# PART 4: INVESTIGATING HOMOPHILY BY LUNCH LOCATION
# ============================================================================

# Load lunch location data (adjust the path to your actual data file)
lunch_data <- read.csv("/Users/<USER>/Documents/Western Sydney Univeristy MBA/QUARTER 2:2025/SOCIAL MEDIA/Assignment/lunch_location2025.csv")

# Function to calculate homophily metrics
calculate_homophily <- function(graph, attribute_data) {
  # Ensure we have both graph and attribute data
  if (vcount(graph) == 0 || nrow(attribute_data) == 0) {
    cat("Cannot calculate homophily: Missing graph or attribute data\n")
    return(NULL)
  }
  
  # Extract usernames from node names (removing domain part)
  node_usernames <- gsub("@.*$", "", V(graph)$name)
  
  # Create a mapping of usernames to locations
  username_to_location <- setNames(
    as.character(attribute_data$Location), 
    attribute_data$Username
  )
  
  # Count edges between same and different lunch locations
  same_location_edges <- 0
  diff_location_edges <- 0
  
  # Get edge list
  edge_list <- as_edgelist(graph)
  
  # For each edge, check if nodes share the same lunch location
  for (i in 1:nrow(edge_list)) {
    # Extract usernames from node names
    from_username <- gsub("@.*$", "", edge_list[i, 1])
    to_username <- gsub("@.*$", "", edge_list[i, 2])
    
    # Get locations
    from_location <- username_to_location[from_username]
    to_location <- username_to_location[to_username]
    
    # Skip if location data is missing
    if (is.na(from_location) || is.na(to_location)) {
      next
    }
    
    if (from_location == to_location) {
      same_location_edges <- same_location_edges + 1
    } else {
      diff_location_edges <- diff_location_edges + 1
    }
  }
  
  # Calculate homophily metrics
  total_counted_edges <- same_location_edges + diff_location_edges
  
  if (total_counted_edges > 0) {
    homophily_index <- same_location_edges / total_counted_edges
    
    # Calculate expected homophily under random mixing
    location_counts <- table(attribute_data$Location)
    location_props <- location_counts / sum(location_counts)
    
    # Expected homophily is sum of squared proportions (probability of same location by chance)
    expected_homophily <- sum(location_props^2)
    
    # Calculate homophily ratio (observed/expected)
    homophily_ratio <- homophily_index / expected_homophily
    
    return(list(
      same_location_edges = same_location_edges,
      diff_location_edges = diff_location_edges,
      total_counted_edges = total_counted_edges,
      homophily_index = homophily_index,
      expected_homophily = expected_homophily,
      homophily_ratio = homophily_ratio
    ))
  } else {
    cat("No edges with complete location data found\n")
    return(NULL)
  }
}

# Visualize network with lunch locations
visualize_homophily <- function(graph, attribute_data) {
  # Extract usernames from node names
  node_usernames <- gsub("@.*$", "", V(graph)$name)
  
  # Create a mapping of usernames to locations
  username_to_location <- setNames(
    as.character(attribute_data$Location), 
    attribute_data$Username
  )
  
  # Assign locations to nodes
  node_locations <- username_to_location[node_usernames]
  
  # Create color mapping for lunch locations
  locations <- unique(attribute_data$Location)
  location_colors <- rainbow(length(locations))
  names(location_colors) <- locations
  
  # Assign colors to nodes based on lunch location
  V(graph)$color <- "gray"  # Default color
  for (i in 1:length(node_usernames)) {
    location <- username_to_location[node_usernames[i]]
    if (!is.na(location)) {
      V(graph)$color[i] <- location_colors[location]
    }
  }
  
  # Plot the network
  plot(graph,
       layout = layout_with_fr(graph),
       vertex.label.cex = 0.6,
       vertex.size = 8,
       edge.arrow.size = 0.3,
       main = "Network Colored by Lunch Location")
  
  # Add legend
  legend("bottomright",
         legend = locations,
         fill = location_colors,
         title = "Lunch Locations",
         cex = 0.8,
         bty = "n")
}

# Run homophily analysis if we have both network and lunch data
if (exists("following_graph") && vcount(following_graph) > 0) {
  cat("\n=== HOMOPHILY ANALYSIS BY LUNCH LOCATION ===\n")
  
  # Check if lunch data exists
  if (exists("lunch_data") && nrow(lunch_data) > 0) {
    cat("Lunch data loaded successfully with", nrow(lunch_data), "entries\n")
    cat("Locations in data:", paste(unique(lunch_data$Location), collapse=", "), "\n\n")
    
    # Calculate homophily metrics
    homophily_results <- calculate_homophily(following_graph, lunch_data)
    
    if (!is.null(homophily_results)) {
      cat("Homophily analysis results:\n")
      cat("Same location connections:", homophily_results$same_location_edges, "\n")
      cat("Different location connections:", homophily_results$diff_location_edges, "\n")
      cat("Homophily index (proportion of same-location connections):", 
          round(homophily_results$homophily_index, 3), "\n")
      cat("Expected homophily under random mixing:", 
          round(homophily_results$expected_homophily, 3), "\n")
      cat("Homophily ratio (observed/expected):", 
          round(homophily_results$homophily_ratio, 3), "\n\n")
      
      if (homophily_results$homophily_ratio > 1.5) {
        cat("CONCLUSION: Strong evidence of homophily by lunch location\n")
        cat("People are much more likely to connect with others who share their lunch location\n")
      } else if (homophily_results$homophily_ratio > 1.1) {
        cat("CONCLUSION: Moderate evidence of homophily by lunch location\n")
        cat("People show some tendency to connect with others who share their lunch location\n")
      } else if (homophily_results$homophily_ratio >= 0.9) {
        cat("CONCLUSION: No evidence of homophily by lunch location\n")
        cat("Connections appear to form independently of lunch location\n")
      } else {
        cat("CONCLUSION: Evidence of heterophily (opposite of homophily)\n")
        cat("People tend to connect with others who have different lunch locations\n")
      }
      
      # Visualize the network with lunch locations
      visualize_homophily(following_graph, lunch_data)
    } else {
      cat("Homophily analysis failed: No matching users found between network and lunch data\n")
      
      # Debug information
      cat("\nDebug information:\n")
      cat("Network node names (first 5):", paste(head(V(following_graph)$name, 5), collapse=", "), "\n")
      cat("Lunch data usernames (first 5):", paste(head(lunch_data$Username, 5), collapse=", "), "\n")
    }
  } else {
    cat("Lunch location data not available. Please load the lunch data file.\n")
    cat("Expected format: CSV with columns 'Username' and 'Location'\n")
  }
} else {
  cat("Network graph not available for homophily analysis\n")
}



# ============================================================================
# PART 5: ACCOUNT POPULARITY USING PAGERANK
# ============================================================================

# Calculate account popularity using PageRank algorithm
calculate_account_popularity <- function(graph, alpha = 0.85) {
  # Ensure the graph is directed
  if (!is_directed(graph)) {
    cat("Converting to directed graph for PageRank calculation\n")
    graph <- as.directed(graph)
  }
  
  # Calculate PageRank with damping factor alpha
  pr_scores <- page_rank(graph, algo = "prpack", damping = alpha)$vector
  
  # Calculate in-degree for comparison
  in_degrees <- degree(graph, mode = "in")
  
  # Calculate eigenvector centrality as another comparison
  eigen_cent <- try(eigen_centrality(graph)$vector, silent = TRUE)
  if (inherits(eigen_cent, "try-error")) {
    eigen_cent <- rep(NA, length(pr_scores))
  }
  
  # Combine results
  popularity_data <- data.frame(
    account = V(graph)$name,
    pagerank = pr_scores,
    in_degree = in_degrees,
    eigen_centrality = eigen_cent,
    stringsAsFactors = FALSE
  )
  
  # Sort by PageRank (descending)
  popularity_data <- popularity_data[order(-popularity_data$pagerank), ]
  
  return(popularity_data)
}

# Run account popularity analysis if we have a network graph
if (exists("following_graph") && vcount(following_graph) > 0) {
  cat("\n=== ACCOUNT POPULARITY ANALYSIS USING PAGERANK ===\n")
  cat("Using Scaled PageRank algorithm with alpha = 0.85\n\n")
  
  # Calculate PageRank and in-degree
  popularity_results <- calculate_account_popularity(following_graph, alpha = 0.85)
  
  # Display top 10 accounts by PageRank
  top_accounts <- head(popularity_results, 10)
  
  # Format for display
  top_accounts$pagerank <- round(top_accounts$pagerank, 4)
  
  # Extract username from full account name for cleaner display
  top_accounts$username <- gsub("@.*$", "", top_accounts$account)
  
  # Reorder columns for display
  display_accounts <- top_accounts[, c("username", "pagerank", "in_degree", "account")]
  
  cat("Top 10 Most Popular Accounts by PageRank:\n")
  print(display_accounts[, c("username", "pagerank", "in_degree")])
  
  # Calculate correlation between PageRank and in-degree
  pr_indegree_cor <- cor(popularity_results$pagerank, popularity_results$in_degree)
  cat("\nCorrelation between PageRank and in-degree:", round(pr_indegree_cor, 3), "\n")
  
  # Identify outliers for analysis
  # Calculate z-scores for PageRank
  popularity_results$pr_zscore <- scale(popularity_results$pagerank)
  outliers <- popularity_results[abs(popularity_results$pr_zscore) > 2, ]
  
  if (nrow(outliers) > 0) {
    cat("\nOutlier accounts (PageRank z-score > 2):\n")
    outliers$username <- gsub("@.*$", "", outliers$account)
    print(outliers[, c("username", "pagerank", "in_degree")])
  }
  
  # Create improved scatter plot comparing PageRank and in-degree
  # Set up plot with better formatting
  par(mar = c(5, 5, 4, 2) + 0.1)  # Increase margins for labels
  
  plot(popularity_results$in_degree, popularity_results$pagerank,
       xlab = "In-degree (Number of Followers)", 
       ylab = "PageRank Score",
       main = "Comparison of PageRank and In-degree",
       pch = 19, col = "blue", cex = 1.2,
       cex.lab = 1.2, cex.axis = 1.1, cex.main = 1.3)
  
  # Add grid for better readability
  grid(lty = "dotted", col = "lightgray")
  
  # Add regression line with confidence interval
  model <- lm(pagerank ~ in_degree, data = popularity_results)
  abline(model, col = "red", lwd = 2)
  
  # Add correlation text to plot with better positioning
  text_x <- max(popularity_results$in_degree) * 0.7
  text_y <- max(popularity_results$pagerank) * 0.8
  text_label <- paste("Correlation:", round(pr_indegree_cor, 2))
  text(text_x, text_y, text_label, cex = 1.2, font = 2)
  
  # Label outliers if any exist
  if (nrow(outliers) > 0) {
    for (i in 1:nrow(outliers)) {
      text(outliers$in_degree[i], outliers$pagerank[i], 
           gsub("@.*$", "", outliers$account[i]),
           pos = 3, cex = 0.8, col = "darkred")
    }
  }
  
  # Analyze differences between PageRank and in-degree rankings
  cat("\nAnalysis of PageRank vs. In-degree Rankings:\n")
  
  # Rank by in-degree
  in_degree_ranking <- popularity_results[order(-popularity_results$in_degree), ]
  in_degree_ranking$in_degree_rank <- 1:nrow(in_degree_ranking)
  
  # Rank by PageRank
  pagerank_ranking <- popularity_results
  pagerank_ranking$pagerank_rank <- 1:nrow(pagerank_ranking)
  
  # Merge rankings
  ranking_comparison <- merge(
    pagerank_ranking[, c("account", "pagerank_rank")],
    in_degree_ranking[, c("account", "in_degree_rank")],
    by = "account"
  )
  
  # Calculate rank difference
  ranking_comparison$rank_diff <- ranking_comparison$in_degree_rank - ranking_comparison$pagerank_rank
  
  # Find accounts with biggest ranking differences
  ranking_comparison <- ranking_comparison[order(-abs(ranking_comparison$rank_diff)), ]
  
  # Display accounts with significant ranking differences (if any exist)
  significant_diff <- ranking_comparison[abs(ranking_comparison$rank_diff) >= 3, ]
  
  if (nrow(significant_diff) > 0) {
    cat("Accounts with significant differences between PageRank and in-degree rankings:\n")
    
    # Add account names for display
    significant_diff$username <- gsub("@.*$", "", significant_diff$account)
    
    # Get full data for these accounts
    sig_accounts_full <- merge(
      significant_diff,
      popularity_results[, c("account", "pagerank", "in_degree")],
      by = "account"
    )
    
    # Display in a readable format
    sig_display <- sig_accounts_full[, c("username", "pagerank_rank", "in_degree_rank", 
                                        "rank_diff", "pagerank", "in_degree")]
    names(sig_display) <- c("Username", "PR Rank", "In-deg Rank", 
                           "Rank Diff", "PageRank", "In-degree")
    
    print(head(sig_display, 5))
    
    cat("\nInterpretation: Accounts ranked higher by PageRank than by in-degree\n")
    cat("are connected to more influential accounts, while accounts ranked higher\n")
    cat("by in-degree simply have more connections regardless of influence.\n")
  } else {
    cat("No significant differences found between PageRank and in-degree rankings.\n")
    cat("This suggests that in this network, the number of followers (in-degree)\n")
    cat("closely aligns with the influence-weighted measure (PageRank).\n")
  }
  
  # Create a second visualization: bar chart comparing top accounts
  top_n <- min(10, nrow(popularity_results))
  top_accounts <- head(popularity_results[order(-popularity_results$pagerank), ], top_n)
  top_accounts$username <- gsub("@.*$", "", top_accounts$account)
  
  # Normalize scores for better comparison
  max_pr <- max(top_accounts$pagerank)
  max_in <- max(top_accounts$in_degree)
  top_accounts$norm_pagerank <- top_accounts$pagerank / max_pr
  top_accounts$norm_in_degree <- top_accounts$in_degree / max_in
  
  # Create side-by-side bar chart
  par(mar = c(7, 4, 4, 2) + 0.1)  # Increase bottom margin for labels
  barplot(
    t(as.matrix(top_accounts[, c("norm_pagerank", "norm_in_degree")])),
    beside = TRUE,
    names.arg = top_accounts$username,
    col = c("darkblue", "skyblue"),
    main = "Top Accounts: PageRank vs. In-degree (Normalized)",
    ylab = "Normalized Score",
    las = 2  # Rotate labels
  )
  legend("topright", 
         legend = c("PageRank", "In-degree"), 
         fill = c("darkblue", "skyblue"),
         bty = "n")
  
  # Conclusion
  cat("\nPageRank Interpretation:\n")
  cat("- PageRank (α = 0.85) measures account popularity considering both direct\n")
  cat("  connections and the influence of those connections\n")
  cat("- Higher PageRank indicates accounts that are followed by other influential accounts\n")
  cat("- The correlation of", round(pr_indegree_cor, 2), "between PageRank and in-degree suggests\n")
  cat("  that in this network, follower count is a strong but not perfect predictor of influence\n")
  cat("- The damping factor (α = 0.85) balances between following the network structure\n")
  cat("  and random jumps, modeling typical user browsing behavior\n")
  
  # Reset plot parameters
  par(mar = c(5, 4, 4, 2) + 0.1)  # Reset to default margins
} else {
  cat("Network graph not available for PageRank analysis\n")
}



# ============================================================================
# PART 6: ACCOUNT SELECTION STRATEGY USING GAME THEORY
# ============================================================================

cat("\n=== ACCOUNT SELECTION STRATEGY ANALYSIS ===\n")

# Define the payoff matrices for each student
student1_payoffs <- matrix(
  c(0.6, 0.2, 0.3, 0.7),
  nrow = 2, ncol = 2,
  dimnames = list(
    c("East", "West"),  # Student 1's strategies
    c("East", "West")   # Student 2's strategies
  )
)

student2_payoffs <- matrix(
  c(0.4, 0.8, 0.9, 0.4),
  nrow = 2, ncol = 2,
  dimnames = list(
    c("East", "West"),  # Student 1's strategies
    c("East", "West")   # Student 2's strategies
  )
)

# Display the payoff matrix
cat("Payoff Matrix (Student 1 payoff, Student 2 payoff):\n")
for (i in 1:2) {
  for (j in 1:2) {
    cat(sprintf("Student 1: %s, Student 2: %s -> (%.1f, %.1f)\n", 
                rownames(student1_payoffs)[i], 
                colnames(student1_payoffs)[j],
                student1_payoffs[i, j],
                student2_payoffs[i, j]))
  }
}

# Check for Nash equilibria
nash_equilibria <- list()
for (i in 1:2) {
  for (j in 1:2) {
    # Check if Student 1 can improve by changing strategy
    s1_current <- student1_payoffs[i, j]
    s1_alternative <- student1_payoffs[3-i, j]
    
    # Check if Student 2 can improve by changing strategy
    s2_current <- student2_payoffs[i, j]
    s2_alternative <- student2_payoffs[i, 3-j]
    
    # It's a Nash equilibrium if neither can improve by changing
    if (s1_current >= s1_alternative && s2_current >= s2_alternative) {
      nash_equilibria[[length(nash_equilibria) + 1]] <- list(
        student1 = rownames(student1_payoffs)[i],
        student2 = colnames(student1_payoffs)[j],
        payoff1 = s1_current,
        payoff2 = s2_current
      )
    }
  }
}

# Display Nash equilibria
cat("\nNash Equilibrium Analysis:\n")
if (length(nash_equilibria) > 0) {
  cat("Found", length(nash_equilibria), "Nash equilibria:\n")
  for (eq in nash_equilibria) {
    cat(sprintf("Student 1: %s, Student 2: %s with payoffs (%.1f, %.1f)\n",
                eq$student1, eq$student2, eq$payoff1, eq$payoff2))
  }
} else {
  cat("No pure strategy Nash equilibrium found\n")
}

# Calculate mixed strategy equilibrium for Student 1
# Let p = probability Student 1 chooses East, 1-p = probability of West
p_student1_east <- 5/9  # Calculated by setting expected payoffs equal
cat("\nMixed Strategy Analysis:\n")
cat("Student 1's optimal strategy: Choose East with probability", 
    round(p_student1_east, 2), "and West with probability", 
    round(1 - p_student1_east, 2), "\n")

# Calculate mixed strategy equilibrium for Student 2
# Let q = probability Student 2 chooses East, 1-q = probability of West
q_student2_east <- 5/8  # Calculated by setting expected payoffs equal
cat("Student 2's optimal strategy: Choose East with probability", 
    round(q_student2_east, 2), "and West with probability", 
    round(1 - q_student2_east, 2), "\n")

# Calculate expected payoff with mixed strategy
expected_payoff_student1 <- 0.45  # Calculated value
expected_payoff_student2 <- 0.60  # Calculated value

cat("\nExpected payoffs with mixed strategy:\n")
cat("Student 1:", round(expected_payoff_student1, 2), "\n")
cat("Student 2:", round(expected_payoff_student2, 2), "\n")

# Create a better visualization of the payoff matrix
library(ggplot2)

# Create data frame for visualization
plot_data <- data.frame(
  student1 = rep(c("East", "West"), each = 2),
  student2 = rep(c("East", "West"), 2),
  x = rep(c(1, 2), each = 2),
  y = rep(c(1, 2), 2),
  payoff1 = c(0.6, 0.2, 0.3, 0.7),
  payoff2 = c(0.4, 0.8, 0.9, 0.4)
)

# Create the plot
ggplot(plot_data, aes(x = student2, y = student1)) +
  geom_tile(fill = "white", color = "black") +
  geom_text(aes(label = paste("S1:", payoff1, "\nS2:", payoff2)), 
            size = 4) +
  labs(title = "Payoff Matrix Visualization",
       x = "Student 2's Strategy",
       y = "Student 1's Strategy") +
  theme_minimal() +
  theme(panel.grid = element_blank(),
        axis.text = element_text(size = 12),
        axis.title = element_text(size = 14),
        plot.title = element_text(size = 16, hjust = 0.5))

# Conclusion and recommendations
cat("\n=== CONCLUSION AND RECOMMENDATIONS ===\n")
cat("Based on the game theory analysis:\n\n")

cat("1. Mixed Strategy Recommendation:\n")
cat("   - Student 1: Visit East with probability 0.56, West with probability 0.44\n")
cat("   - Student 2: Visit East with probability 0.63, West with probability 0.37\n")
cat("   - Expected chance of joining social groups: Student 1 = 0.45, Student 2 = 0.60\n\n")

cat("2. Implications for Department of Education:\n")
cat("   - Students should use a mixed strategy approach\n")
cat("   - Orientation programs should guide students to visit both campus sides\n")
cat("   - This maximizes chances of joining social groups and improves campus integration\n")

# Reset plot parameters
par(mar = c(5, 4, 4, 2) + 0.1)

# ============================================================================
# PART 7: CONCLUSIONS AND INSIGHTS
# ============================================================================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("SOCIAL MEDIA ANALYTICS SUMMARY - MASTODON ANALYSIS\n")
cat(paste(rep("=", 60), collapse = ""), "\n\n")

cat("1. INSTANCE OVERVIEW:\n")
if (exists("instance_info")) {
  cat("   - Instance: mastodon.social\n")
  cat("   - Analysis Date:", format(Sys.Date(), "%Y-%m-%d"), "\n")
}

cat("\n2. ACTIVITY PATTERNS:\n")
if (exists("instance_activity") && nrow(instance_activity) > 0) {
  cat("   - Average weekly posts:",
      round(mean(instance_activity$statuses)), "\n")
  cat("   - Average weekly logins:",
      round(mean(instance_activity$logins)), "\n")
}

cat("\n3. CONTENT ANALYSIS:\n")
if (exists("public_timeline") && nrow(public_timeline) > 0) {
  cat("   - Public timeline posts analyzed:", nrow(public_timeline), "\n")
}
if (exists("rstats_posts") && nrow(rstats_posts) > 0) {
  cat("   - #rstats posts analyzed:", nrow(rstats_posts), "\n")
}

cat("\n4. KEY INSIGHTS:\n")
cat("   - Mastodon shows decentralized social media engagement patterns\n")
cat("   - Technical communities (#rstats) show specific behaviors\n")
cat("   - Posting patterns vary by time of day and content type\n")

cat("\nAnalysis completed using rtoot package for R\n")
cat("Western Sydney University MBA - Social Media Analytics\n")
